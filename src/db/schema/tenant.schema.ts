import {pgTable, text, timestamp, boolean} from "drizzle-orm/pg-core";

export const tenant = pgTable("tenant", {
    id: text("id").primaryKey(),
    name: text("name").notNull(),
    email: text("email").notNull().unique(),
    createdAt: timestamp("created_at")
        .$defaultFn(() => new Date())
        .notNull(),
    updatedAt: timestamp("updated_at")
        .$defaultFn(() => new Date())
        .notNull(),
    isActive: boolean("is_active").$defaultFn(() => true).notNull(),
});

