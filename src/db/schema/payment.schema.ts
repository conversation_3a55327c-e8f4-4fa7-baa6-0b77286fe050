import {boolean, pgTable, text, timestamp, doublePrecision} from "drizzle-orm/pg-core";

export const payment = pgTable("payment", {
    id: text("id").primaryKey(),
    createdAt: timestamp("created_at")
        .$defaultFn(() => new Date())
        .notNull(),
    updatedAt: timestamp("updated_at")
        .$defaultFn(() => new Date())
        .notNull(),
    amount: text("amount").notNull(),
    currency: text("currency").notNull(),
    description: text("description").notNull(),
    status: text("status").notNull(),
    stripePaymentIntentId: text("stripe_payment_intent_id").notNull(),
    stripeCustomerId: text("stripe_customer_id").notNull(),
    tenantId: text("tenant_id").notNull(), // receiver
    userId: text("user_id").notNull(), // sender
});
