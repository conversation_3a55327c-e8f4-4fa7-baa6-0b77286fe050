import {NextResponse} from "next/server";
import {db} from "@/db";
import {tenant} from "@/db/schema";

export async function POST(request: Request) {
    const body = await request.json()
    const { name, email } = body

    if (!name || !email) {
        return NextResponse.json({ error: "Name, IBAN und Adresse sind erforderlich" }, { status: 400 })
    }

    await db.insert(tenant).values(body).catch(console.error);
}
