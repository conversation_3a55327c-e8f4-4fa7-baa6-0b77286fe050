import {NextResponse} from "next/server";
import {db} from "@/db";
import {payment} from "@/db/schema";

export async function POST(request: Request) {
    const body = await request.json();
    const { amount, currency, description, tenant_id, user_id } = body;
    if (!amount || !currency || !tenant_id || !user_id) {
        return NextResponse.json({ error: "Amount, currency, tenant_id and client_id are required" }, { status: 400 });
    }

    const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [{
            price_data: {
                currency: currency,
                product_data: {
                    name: description || 'Payment',
                },
                unit_amount: amount * 100,
            },
            quantity: 1,
        }],
        mode: 'payment',
        success_url: `${process.env.NEXT_PUBLIC_BASE_URL}/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/cancel`,
        metadata: {
            tenant_id,
            user_id,
        },
    });

    db.insert(payment).values({
        amount: String(amount),
        currency: String(currency),
        description,
        tenantId: tenant_id,
        userId: user_id,
        stripePaymentIntentId: session.id,
        stripeCustomerId: session.customerId,
        status: 'paid' // Add the required 'status' field with a value
    }).catch((error) => {
        console.error("Error inserting payment record:", error);
        return NextResponse.json({ error: "Failed to create payment record" }, { status: 500 });
    })

    return NextResponse.json({ sessionId: session.id }, { status: 200 });
}
