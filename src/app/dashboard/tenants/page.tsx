import { TenantsTable } from "@/components/tables/tenants-table";
import { columns } from "@/components/tables/tenants-table/columns";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "@radix-ui/react-icons";
import type { NextPage } from "next";

const TenantsPage: NextPage = () => {
	return (
		<div className="space-y-12">
			<div className="flex items-end justify-between">
				<div className="space-y-2.5">
					<h1 className="text-3xl font-semibold">Kunden</h1>
					<p className="text-muted-foreground max-w-xl text-balance">
						Hier kannst du deine Kunden verwalten.
					</p>
				</div>
				<div>
					<Button variant={"outline"}>
						<PlusIcon />
						Kunden hinzufügen
					</Button>
				</div>
			</div>
			<div>
				<TenantsTable data={[]} columns={columns} />
			</div>
		</div>
	);
};

export default TenantsPage;
