import type { <PERSON> } from "react";
import { UserProfile } from "@/components/auth";
import Link from "next/link";

export const Nav: FC = () => {
	return (
		<div className="flex h-16 items-center justify-between px-8">
			<div className="flex h-full items-center gap-x-8">
				<Link href="/">
					<div>Zaiply&trade;</div>
				</Link>
			</div>
			<div className="flex h-full items-center gap-x-8">
				<UserProfile />
			</div>
		</div>
	);
};
